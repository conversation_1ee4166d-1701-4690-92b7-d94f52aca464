import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function ShadcnTest() {
  return (
    <div className="p-8 space-y-6">
      <h2 className="text-2xl font-bold">Shadcn UI Test</h2>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Shadcn Setup Test</CardTitle>
          <CardDescription>Testing if shadcn components are working properly</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            If you can see this card with proper styling, shadcn is working correctly!
          </p>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Button Variants</h3>
        <div className="space-x-2">
          <Button>Default Button</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="link">Link</Button>
        </div>
        <div className="space-x-2">
          <Button size="sm">Small</Button>
          <Button size="default">Default</Button>
          <Button size="lg">Large</Button>
        </div>
      </div>
    </div>
  )
}
