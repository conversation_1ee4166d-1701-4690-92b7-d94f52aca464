import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

describe('Theme Integration', () => {
  it('should have correct CSS variables for sidebar in light mode', () => {
    // Create a test element to check CSS variables
    const testElement = document.createElement('div')
    document.body.appendChild(testElement)
    
    // Remove dark class to ensure light mode
    document.documentElement.classList.remove('dark')
    
    // Get computed styles
    const styles = getComputedStyle(document.documentElement)
    
    // Check that sidebar variables are set correctly for light mode
    const sidebarColor = styles.getPropertyValue('--sidebar').trim()
    const sidebarForeground = styles.getPropertyValue('--sidebar-foreground').trim()
    
    // Should be dark blue for sidebar background
    expect(sidebarColor).toContain('oklch')
    expect(sidebarColor).toContain('0.15') // Dark blue lightness
    
    // Should be white for text
    expect(sidebarForeground).toContain('oklch')
    expect(sidebarForeground).toContain('0.984') // White lightness
    
    document.body.removeChild(testElement)
  })

  it('should have correct CSS variables for sidebar in dark mode', () => {
    // Create a test element to check CSS variables
    const testElement = document.createElement('div')
    document.body.appendChild(testElement)
    
    // Add dark class to ensure dark mode
    document.documentElement.classList.add('dark')
    
    // Get computed styles
    const styles = getComputedStyle(document.documentElement)
    
    // Check that sidebar variables are set correctly for dark mode
    const sidebarColor = styles.getPropertyValue('--sidebar').trim()
    const sidebarForeground = styles.getPropertyValue('--sidebar-foreground').trim()
    
    // Should be slightly lighter blue for dark mode
    expect(sidebarColor).toContain('oklch')
    expect(sidebarColor).toContain('0.18') // Slightly lighter blue
    
    // Should still be white for text
    expect(sidebarForeground).toContain('oklch')
    expect(sidebarForeground).toContain('0.984') // White lightness
    
    // Clean up
    document.documentElement.classList.remove('dark')
    document.body.removeChild(testElement)
  })

  it('should have brand-nav color consistent in both modes', () => {
    const testElement = document.createElement('div')
    document.body.appendChild(testElement)
    
    // Test light mode
    document.documentElement.classList.remove('dark')
    let styles = getComputedStyle(document.documentElement)
    const lightBrandNav = styles.getPropertyValue('--brand-nav').trim()
    
    // Test dark mode
    document.documentElement.classList.add('dark')
    styles = getComputedStyle(document.documentElement)
    const darkBrandNav = styles.getPropertyValue('--brand-nav').trim()
    
    // Should be the same dark blue in both modes
    expect(lightBrandNav).toBe(darkBrandNav)
    expect(lightBrandNav).toContain('202 88% 13%')
    
    // Clean up
    document.documentElement.classList.remove('dark')
    document.body.removeChild(testElement)
  })

  it('should have green card colors in both light and dark modes', () => {
    const testElement = document.createElement('div')
    document.body.appendChild(testElement)

    // Test light mode
    document.documentElement.classList.remove('dark')
    let styles = getComputedStyle(document.documentElement)
    const lightCardColor = styles.getPropertyValue('--card').trim()

    // Test dark mode
    document.documentElement.classList.add('dark')
    styles = getComputedStyle(document.documentElement)
    const darkCardColor = styles.getPropertyValue('--card').trim()

    // Both should contain green color values (145 hue for green)
    expect(lightCardColor).toContain('145') // Green hue in HSL
    expect(darkCardColor).toContain('145') // Green hue in HSL

    // Light mode should be darker green, dark mode should be lighter green
    expect(lightCardColor).toContain('26%') // Darker green for light mode
    expect(darkCardColor).toContain('35%') // Lighter green for dark mode

    // Clean up
    document.documentElement.classList.remove('dark')
    document.body.removeChild(testElement)
  })
})
