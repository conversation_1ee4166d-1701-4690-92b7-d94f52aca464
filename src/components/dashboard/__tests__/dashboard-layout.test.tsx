import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { DashboardLayout } from '../dashboard-layout'

// Mock the dependencies
vi.mock('../dashboard-sidebar', () => ({
  DashboardSidebar: () => <div data-testid="dashboard-sidebar">Sidebar</div>
}))

vi.mock('../../contexts/theme-context', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: vi.fn()
  })
}))

vi.mock('../../ui/sidebar', () => ({
  SidebarProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SidebarTrigger: ({ className }: { className?: string }) => (
    <button className={className} data-testid="sidebar-trigger">Toggle</button>
  ),
  useSidebar: () => ({
    state: 'expanded'
  })
}))

vi.mock('../../ui/separator', () => ({
  Separator: ({ orientation, className }: { orientation?: string; className?: string }) => (
    <div className={className} data-testid={`separator-${orientation}`} />
  )
}))

vi.mock('../../ui/button', () => ({
  Button: ({ children, className, ...props }: any) => (
    <button className={className} {...props}>{children}</button>
  )
}))

describe('DashboardLayout', () => {
  it('renders header with correct z-index styling', () => {
    render(
      <DashboardLayout title="Test Dashboard" description="Test description">
        <div>Test content</div>
      </DashboardLayout>
    )

    // Find the header element
    const header = screen.getByRole('banner')
    
    // Check that the header has the correct classes including z-index
    expect(header).toHaveClass('relative', 'z-20')
    expect(header).toHaveClass('flex', 'h-16', 'shrink-0', 'items-center', 'gap-2', 'border-b', 'border-border', 'bg-background', 'px-4')
  })

  it('renders sidebar trigger and separator', () => {
    render(
      <DashboardLayout title="Test Dashboard">
        <div>Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('sidebar-trigger')).toBeInTheDocument()
    expect(screen.getByTestId('separator-vertical')).toBeInTheDocument()
  })

  it('renders title and description when provided', () => {
    render(
      <DashboardLayout title="Test Dashboard" description="Test description">
        <div>Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByText('Test Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
  })

  it('renders children content', () => {
    render(
      <DashboardLayout>
        <div data-testid="test-content">Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('test-content')).toBeInTheDocument()
  })

  it('renders sidebar component', () => {
    render(
      <DashboardLayout>
        <div>Test content</div>
      </DashboardLayout>
    )

    expect(screen.getByTestId('dashboard-sidebar')).toBeInTheDocument()
  })
})
