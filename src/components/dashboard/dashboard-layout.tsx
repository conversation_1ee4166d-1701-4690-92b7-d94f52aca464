import React from 'react'
import { SidebarProvider, SidebarTrigger, useSidebar } from '../ui/sidebar.tsx'
import { DashboardSidebar } from './dashboard-sidebar.tsx'
import { Separator } from '../ui/separator.tsx'
import { useTheme } from '../../contexts/theme-context.tsx'
import { Button } from '../ui/button.tsx'
import { Moon, Sun, Monitor } from 'lucide-react'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
}

interface DashboardHeaderProps {
  title?: string
  description?: string
}

function DashboardHeader({ title, description }: DashboardHeaderProps) {
  const { theme, setTheme } = useTheme()
  const { state } = useSidebar()

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="h-4 w-4" />
      case 'dark':
        return <Moon className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const cycleTheme = () => {
    const themes = ['light', 'dark', 'system'] as const
    const currentIndex = themes.indexOf(theme)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border bg-background px-4">
      <SidebarTrigger className="-ml-1" />
      <Separator orientation="vertical" className="mr-2 h-4" />

      <div className="flex flex-1 items-center justify-between">
        <div className={`flex flex-col transition-all duration-200 ease-linear ${
          state === 'collapsed' ? 'ml-0' : 'ml-2'
        }`}>
          {title && (
            <h1 className="text-lg font-semibold text-foreground">
              {title}
            </h1>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={cycleTheme}
            className="h-8 w-8 p-0"
            title={`Current theme: ${theme}. Click to cycle.`}
          >
            {getThemeIcon()}
          </Button>
        </div>
      </div>
    </header>
  )
}

export function DashboardLayout({ children, title, description }: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <DashboardSidebar />

        <div className="flex flex-1 flex-col">
          <DashboardHeader title={title} description={description} />

          {/* Main Content */}
          <main className="flex-1 overflow-auto bg-background">
            <div className="container mx-auto p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}
