{"name": "fme-start", "private": true, "type": "module", "scripts": {"dev": "vite dev --port 3000", "start": "node .output/server/index.mjs", "build": "vite build", "serve": "vite preview", "test": "vitest run"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-devtools": "^0.2.2", "@tanstack/react-form": "^1.19.3", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.131.5", "@tanstack/react-router-ssr-query": "^1.131.7", "@tanstack/react-start": "^1.131.7", "@tanstack/router-plugin": "^1.121.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.0.6", "tailwindcss-animate": "^1.0.7", "vite-tsconfig-paths": "^5.1.4"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "shadcn": "^3.0.0", "typescript": "^5.7.2", "vite": "^6.3.5", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}